import http from './index'

// 房源选项接口定义（基于实际返回的数据结构）
export interface RoomOption {
    id: string | null
    name: string | null
    parentId: string | null
    roomId: string | null
    roomName: string | null
    projectId: string | null
    projectName: string | null
    stageId: string | null
    stageName: string | null
    parcelId: string | null
    parcelName: string | null
    buildingId: string | null
    buildingName: string | null
    floorId: string | null
    floorName: string | null
    propertyType: string | null
    rentStatus: string | null
    rentAreaType: number | null
    rentArea: number | null
    price: number | null
    bottomPrice: number | null
    priceUnit: number | null
    externalRentStartDate: string | null
    depositType: number | null
    depositAmount: number | null
    level: number
    children: RoomOption[] | null
}

// 房源选项查询参数
export interface RoomOptionsParams {
    projectId: string
    buildingType?: string // 业态类型（对应意向业态）
    businessType?: string // 业态类型（兼容性保留）
    keyword?: string // 搜索关键词
    status?: string | number // 房源状态
    pageNum?: number
    pageSize?: number
}

// 房源选项响应数据（树形结构）
export interface RoomOptionsResponse extends Array<RoomOption> {}

/**
 * 根据项目ID获取房源选项
 * @param params 查询参数，其中buildingType对应意向业态类型用于筛选房源
 * @returns 房源选项树形列表
 */
export const getRoomOptions = (params: RoomOptionsParams) => {
    return http.post<RoomOptionsResponse>('/business-rent-admin/room/roomOptions', params)
}

/**
 * 根据项目ID获取房源树形结构
 * @param projectId 项目ID
 * @param businessType 业态类型（可选）
 * @returns 房源树形数据
 */
export const getRoomTree = (projectId: string, businessType?: string) => {
    const params: any = { projectId }
    if (businessType) {
        params.businessType = businessType
    }
    return http.get<RoomOption[]>('/business-rent-admin/room/roomTree', params)
}

/**
 * 根据房源ID获取房源详情
 * @param roomId 房源ID
 * @returns 房源详情
 */
export const getRoomDetail = (roomId: string) => {
    return http.get<RoomOption>(`/business-rent-admin/room/${roomId}`)
}

/**
 * 从树形结构中提取所有实际房源（level: 4）
 * @param treeData 树形数据
 * @returns 扁平化的房源列表
 */
export const extractRoomsFromTree = (treeData: RoomOption[]): RoomOption[] => {
    const rooms: RoomOption[] = []
    
    const traverse = (nodes: RoomOption[]) => {
        for (const node of nodes) {
            // level: 4 表示实际的房源
            if (node.level === 4 && node.roomId && node.roomName) {
                rooms.push(node)
            }
            
            // 递归处理子节点
            if (node.children && node.children.length > 0) {
                traverse(node.children)
            }
        }
    }
    
    traverse(treeData)
    return rooms
} 