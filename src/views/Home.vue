<script lang="ts">
  export default {
    name: 'Home'
  }
</script>
<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
// 这些组件在模板中被自动解析，所以无需显式导入
// import { NavBar, Cell, CellGroup, Button, Icon } from 'vant'

const router = useRouter()

// 功能菜单列表
const menuList = ref([
  // { 
  //   title: 'CRM仪表板', 
  //   desc: '维享信CRM管理平台', 
  //   path: '/crm-dashboard',
  //   icon: 'chart-trending-o' 
  // },
  { 
    title: '定单缴费', 
    desc: '查看和缴纳定单费用', 
    path: '/order-payment',
    icon: 'balance-pay' 
  },
  { 
    title: '账单缴费', 
    desc: '查看和缴纳账单费用', 
    path: '/bill-payment',
    icon: 'bill' 
  },
  { 
    title: '进场通知单', 
    desc: '查看进场房源信息', 
    path: '/entrance-notice',
    icon: 'sign' 
  },
  { 
    title: '物业交割单确认', 
    desc: '查看物业交割单', 
    path: '/property-handover',
    icon: 'certificate' 
  },
  { 
    title: '退场单签字确认', 
    desc: '查看和确认退场单', 
    path: '/exit-form-signature',
    icon: 'sign' 
  },
  { 
    title: '出场管理', 
    desc: '出场申请和办理管理', 
    path: '/exit-management',
    icon: 'completed' 
  },
  { 
    title: '定单管理', 
    desc: '定单列表和状态管理', 
    path: '/booking-list',
    icon: 'orders-o' 
  }
])

// 页面跳转
const navigateTo = (path: string) => {
  router.push(path)
}
</script>


<template>
  <div class="home-page">
    <van-nav-bar
      title="万洋资管平台"
      fixed
      placeholder
    />
    
    <div class="home-content">
      <div class="welcome-banner">
        <div class="banner-content">
          <h1 class="welcome-title">欢迎使用万洋资管平台</h1>
          <p class="welcome-desc">为您提供便捷的缴费服务</p>
        </div>
      </div>
      
      <div class="menu-section">
        <h2 class="section-title">服务菜单</h2>
        
        <van-cell-group inset style="margin: 0;">
          <van-cell 
            v-for="(item, index) in menuList" 
            :key="index"
            :title="item.title"
            :value="item.desc"
            is-link
            center
            @click="navigateTo(item.path)"
          >
            <template #icon>
              <div class="menu-icon">
                <van-icon :name="item.icon" size="24" color="#3583FF" />
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

.home-content {
  flex: 1;
  padding: 32px;
}

.welcome-banner {
  height: 280px;
  background: linear-gradient(to right, #3583FF, #5CBFFF);
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.banner-content {
  color: #fff;
}

.welcome-title {
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 8px;
}

.welcome-desc {
  font-size: 32px;
  opacity: 0.8;
}

.menu-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 36px;
  margin-bottom: 32px;
  color: #333;
  font-weight: 500;
  padding-left: 16px;
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(53, 131, 255, 0.1);
  border-radius: 50%;
  margin-right: 8px;
}

.test-div {
  width: 100px;
  height: 100px;
  background-color: red;
}
</style> 