<script lang="ts">
  export default {
    name: 'Home'
  }
</script>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getHomeStats, getUserProjects, getCurrentProject, setCurrentProject } from '../api/home'
import type { HomeStatsData, ProjectInfo } from '../api/home'

const router = useRouter()

// 数据状态
const loading = ref(false)
const statsData = ref<HomeStatsData | null>(null)
const currentProject = ref<ProjectInfo | null>(null)
const projectList = ref<ProjectInfo[]>([])
const showProjectPicker = ref(false)

// 主要功能模块
const mainModules = ref([
  {
    title: '房态图',
    icon: 'home-o',
    color: '#4A90E2',
    path: '/room-status'
  },
  {
    title: '定单管理',
    icon: 'orders-o',
    color: '#F5A623',
    path: '/booking-list'
  },
  {
    title: '进场管理',
    icon: 'sign',
    color: '#50E3C2',
    path: '/entry-management'
  },
  {
    title: '出场管理',
    icon: 'completed',
    color: '#B8860B',
    path: '/exit-management'
  }
])

// 页面跳转
const navigateTo = (path: string) => {
  router.push(path)
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await getUserProjects()
    if (response.code === 200) {
      projectList.value = response.data
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

// 获取当前项目
const fetchCurrentProject = async () => {
  try {
    const response = await getCurrentProject()
    if (response.code === 200) {
      currentProject.value = response.data
    } else {
      // 使用默认项目
      currentProject.value = {
        id: '1',
        name: '福州(马尾)万洋广场',
        shortName: '福州万洋'
      }
    }
  } catch (error) {
    console.error('获取当前项目失败:', error)
    // 使用默认项目
    currentProject.value = {
      id: '1',
      name: '福州(马尾)万洋广场',
      shortName: '福州万洋'
    }
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    loading.value = true
    const response = await getHomeStats(currentProject.value?.id)
    if (response.code === 200) {
      statsData.value = response.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 使用模拟数据
    statsData.value = {
      todoStats: {
        pendingBooking: 14,
        waitingBooking: 0,
        waitingEntry: 2,
        waitingExit: 8,
        exitProcessing: 2
      },
      roomStats: {
        residential: {
          rented: 1234,
          vacant: 235,
          total: 2360,
          rentedOrSignedOrReserved: 235
        },
        factory: {
          rented: 1234,
          vacant: 235,
          total: 2360,
          rentedOrSignedOrReserved: 235
        },
        commercial: {
          rented: 1234,
          vacant: 235,
          total: 2360,
          rentedOrSignedOrReserved: 235
        }
      }
    }
  } finally {
    loading.value = false
  }
}

// 项目选择
const onProjectSelect = async (project: ProjectInfo) => {
  currentProject.value = project
  showProjectPicker.value = false
  try {
    await setCurrentProject(project.id)
    await fetchStats()
  } catch (error) {
    console.error('设置项目失败:', error)
  }
}

// 初始化数据
const initData = async () => {
  await fetchCurrentProject()
  await fetchProjects()
  await fetchStats()
}

// 页面加载时初始化
onMounted(() => {
  initData()
})
</script>


<template>
  <div class="home-page">
    <!-- 顶部项目选择器 -->
    <div class="header-section">
      <div class="project-selector" @click="showProjectPicker = true">
        <van-icon name="location-o" size="32" color="#fff" />
        <span class="project-name">{{ currentProject?.name || '选择项目' }}</span>
        <van-icon name="arrow-down" size="24" color="#fff" />
      </div>
    </div>

    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-text">
        <h2>欢迎回来，张一凡</h2>
      </div>
      <div class="logo-section">
        <div class="logo-text">万洋</div>
        <div class="city-skyline">
          <div class="building" v-for="i in 8" :key="i" :style="{ height: Math.random() * 40 + 20 + 'px' }"></div>
        </div>
      </div>
    </div>

    <!-- 主要功能模块 -->
    <div class="main-modules">
      <div
        class="module-item"
        v-for="(module, index) in mainModules"
        :key="index"
        @click="navigateTo(module.path)"
      >
        <div class="module-icon" :style="{ backgroundColor: module.color }">
          <van-icon :name="module.icon" size="48" color="#fff" />
        </div>
        <span class="module-title">{{ module.title }}</span>
      </div>
    </div>

    <!-- 待办事项 -->
    <div class="section">
      <div class="section-header">
        <van-icon name="bell" size="32" color="#1989fa" />
        <span class="section-title">待办</span>
      </div>

      <div class="todo-grid" v-if="statsData">
        <div class="todo-item">
          <div class="todo-label">待生效定单</div>
          <div class="todo-count">{{ statsData.todoStats.pendingBooking }}</div>
        </div>
        <div class="todo-item">
          <div class="todo-label">待签定单</div>
          <div class="todo-count">{{ statsData.todoStats.waitingBooking }}</div>
        </div>
        <div class="todo-item">
          <div class="todo-label">待进场</div>
          <div class="todo-count">{{ statsData.todoStats.waitingEntry }}</div>
        </div>
        <div class="todo-item">
          <div class="todo-label">待出场</div>
          <div class="todo-count">{{ statsData.todoStats.waitingExit }}</div>
        </div>
        <div class="todo-item">
          <div class="todo-label">出场办理中</div>
          <div class="todo-count">{{ statsData.todoStats.exitProcessing }}</div>
        </div>
      </div>
    </div>

    <!-- 房态统计 -->
    <div class="section">
      <div class="section-header">
        <van-icon name="home-o" size="32" color="#1989fa" />
        <span class="section-title">房态</span>
        <span class="unit-text">面积单位：m²</span>
      </div>

      <div class="room-stats" v-if="statsData">
        <!-- 住宅 -->
        <div class="room-type-item">
          <div class="room-type-icon residential">
            <van-icon name="home-o" size="40" color="#fff" />
          </div>
          <div class="room-type-content">
            <div class="room-type-header">
              <span class="room-type-label">住宅</span>
              <div class="room-counts">
                <span>在租 {{ statsData.roomStats.residential.rented }}</span>
                <span>空置 {{ statsData.roomStats.residential.vacant }}</span>
                <span>合计 {{ statsData.roomStats.residential.total }}</span>
              </div>
            </div>
            <div class="room-type-footer">
              <span class="sub-text">待生效/签约中/已预定 {{ statsData.roomStats.residential.rentedOrSignedOrReserved }}</span>
            </div>
          </div>
        </div>

        <!-- 厂房 -->
        <div class="room-type-item">
          <div class="room-type-icon factory">
            <van-icon name="shop-o" size="40" color="#fff" />
          </div>
          <div class="room-type-content">
            <div class="room-type-header">
              <span class="room-type-label">厂房</span>
              <div class="room-counts">
                <span>在租 {{ statsData.roomStats.factory.rented }}</span>
                <span>空置 {{ statsData.roomStats.factory.vacant }}</span>
                <span>合计 {{ statsData.roomStats.factory.total }}</span>
              </div>
            </div>
            <div class="room-type-footer">
              <span class="sub-text">待生效/签约中/已预定 {{ statsData.roomStats.factory.rentedOrSignedOrReserved }}</span>
            </div>
          </div>
        </div>

        <!-- 商铺 -->
        <div class="room-type-item">
          <div class="room-type-icon commercial">
            <van-icon name="shop-collect-o" size="40" color="#fff" />
          </div>
          <div class="room-type-content">
            <div class="room-type-header">
              <span class="room-type-label">商铺</span>
              <div class="room-counts">
                <span>在租 {{ statsData.roomStats.commercial.rented }}</span>
                <span>空置 {{ statsData.roomStats.commercial.vacant }}</span>
                <span>合计 {{ statsData.roomStats.commercial.total }}</span>
              </div>
            </div>
            <div class="room-type-footer">
              <span class="sub-text">待生效/签约中/已预定 {{ statsData.roomStats.commercial.rentedOrSignedOrReserved }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目选择弹窗 -->
    <van-popup v-model:show="showProjectPicker" position="bottom" round>
      <div class="project-picker">
        <div class="picker-header">
          <span class="picker-title">选择项目</span>
          <van-icon name="cross" size="36" @click="showProjectPicker = false" />
        </div>
        <div class="project-list">
          <div
            class="project-item"
            v-for="project in projectList"
            :key="project.id"
            :class="{ active: project.id === currentProject?.id }"
            @click="onProjectSelect(project)"
          >
            <span>{{ project.name }}</span>
            <van-icon v-if="project.id === currentProject?.id" name="success" size="32" color="#1989fa" />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

/* 顶部项目选择器 */
.header-section {
  padding: 88px 32px 32px;
}

.project-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
}

.project-name {
  font-size: 32px;
  font-weight: 500;
}

/* 欢迎区域 */
.welcome-section {
  padding: 0 32px 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  font-size: 36px;
  font-weight: 400;
  margin: 0;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-text {
  font-size: 96px;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
}

.city-skyline {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  height: 80px;
}

.building {
  width: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px 2px 0 0;
}

/* 主要功能模块 */
.main-modules {
  background: #fff;
  margin: 0 32px 32px;
  border-radius: 24px;
  padding: 40px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.module-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: transform 0.2s;
}

.module-item:active {
  transform: scale(0.95);
}

.module-icon {
  width: 96px;
  height: 96px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-title {
  font-size: 24px;
  color: #333;
  text-align: center;
}

/* 通用区块样式 */
.section {
  background: #fff;
  margin: 0 32px 32px;
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 500;
  color: #333;
}

.unit-text {
  margin-left: auto;
  font-size: 24px;
  color: #999;
}

/* 待办事项 */
.todo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.todo-item {
  text-align: center;
  padding: 24px 16px;
  background: #f8f9fa;
  border-radius: 16px;
}

.todo-label {
  font-size: 24px;
  color: #666;
  margin-bottom: 8px;
}

.todo-count {
  font-size: 40px;
  font-weight: bold;
  color: #333;
}

/* 房态统计 */
.room-stats {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.room-type-item {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 16px;
}

.room-type-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.room-type-icon.residential {
  background: #ff6b6b;
}

.room-type-icon.factory {
  background: #4ecdc4;
}

.room-type-icon.commercial {
  background: #45b7d1;
}

.room-type-content {
  flex: 1;
}

.room-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.room-type-label {
  font-size: 28px;
  font-weight: 500;
  color: #333;
}

.room-counts {
  display: flex;
  gap: 24px;
  font-size: 24px;
  color: #666;
}

.room-type-footer {
  font-size: 22px;
  color: #999;
}

/* 项目选择弹窗 */
.project-picker {
  padding: 40px;
  max-height: 800px;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 2px solid #eee;
}

.picker-title {
  font-size: 32px;
  font-weight: 500;
  color: #333;
}

.project-list {
  max-height: 600px;
  overflow-y: auto;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 0;
  border-bottom: 2px solid #f5f5f5;
  cursor: pointer;
  color: #333;
}

.project-item:last-child {
  border-bottom: none;
}

.project-item.active {
  color: #1989fa;
}

.project-item:active {
  background: #f5f5f5;
}
</style>