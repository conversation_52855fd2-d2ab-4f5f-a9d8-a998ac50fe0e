<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 页面状态
const loading = ref(false)
const searchValue = ref('')
const activeTab = ref('待办理')
const currentFilter = ref('近3天')

// 出场提示弹窗状态
const showExitDialog = ref(false)
const currentExitItem = ref<any>(null)

// 筛选选项
const filterOptions = [
    { text: '近3天', value: '近3天' },
    { text: '近7天', value: '近7天' },
    { text: '近15天', value: '近15天' }
]

// Tab选项
const tabOptions = ['待办理', '办理中', '已办理']

// 出场管理列表数据
const exitList = reactive([
    {
        id: '1',
        contractNo: 'WYSF--2025-0223',
        tenant: '惠州市拾玖电子商务有限公司',
        exitDate: '2025-04-15',
        roomCount: 8,
        status: '待办理'
    },
    {
        id: '2', 
        contractNo: 'WYSF--2025-0223',
        tenant: '惠州市拾玖电子商务有限公司',
        exitDate: '2025-04-15',
        roomCount: 8,
        status: '办理中'
    }
])

// 过滤后的列表
const filteredList = computed(() => {
    return exitList.filter(item => {
        // Tab筛选
        if (item.status !== activeTab.value) {
            return false
        }
        
        // 搜索筛选
        if (searchValue.value) {
            const keyword = searchValue.value.toLowerCase()
            return item.contractNo.toLowerCase().includes(keyword) ||
                   item.tenant.toLowerCase().includes(keyword)
        }
        
        return true
    })
})

// 返回上一页
const onClickLeft = () => {
    router.back()
}

// 搜索
const onSearch = () => {
    // 触发搜索
}

// Tab切换
const onTabChange = (tabName: string) => {
    activeTab.value = tabName
}

// 筛选切换
const onFilterChange = (value: string) => {
    currentFilter.value = value
}

// 办理出场
const handleExit = (item: any) => {
    currentExitItem.value = item
    showExitDialog.value = true
}

// 先物业交割，后续结算
const handlePropertyFirst = () => {
    showExitDialog.value = false
    showToast('跳转到物业交割页面')
    // TODO: 跳转到物业交割页面
}

// 物业交割并结算
const handlePropertyAndSettlement = () => {
    showExitDialog.value = false
    showToast('跳转到物业交割并结算页面')
    // TODO: 跳转到物业交割并结算页面
}

// 取消操作
const handleCancel = () => {
    showExitDialog.value = false
    currentExitItem.value = null
}

// 页面初始化
onMounted(() => {
    // 获取出场管理列表数据
})
</script>

<template>
    <div class="exit-management">
        <!-- 顶部导航栏 -->
        <van-nav-bar title="出场管理" left-arrow fixed placeholder @click-left="onClickLeft" />

        <div class="page-content">
            <!-- 搜索框 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="请输入楼栋/房源/承租方搜索"
                    @search="onSearch"
                    @cancel="searchValue = ''"
                    background="#f7f8fa"
                    show-action
                />
            </div>

            <!-- Tab切换和筛选 -->
            <div class="filter-section">
                <div class="tab-filter-wrapper">
                    <!-- Tab选项 -->
                    <div class="tab-section">
                        <van-tabs
                            v-model:active="activeTab"
                            @change="onTabChange"
                            line-width="40px"
                            line-height="3px"
                            color="#3583FF"
                        >
                            <van-tab
                                v-for="tab in tabOptions"
                                :key="tab"
                                :title="tab"
                                :name="tab"
                            />
                        </van-tabs>
                    </div>

                    <!-- 时间筛选 -->
                    <div class="filter-dropdown">
                        <van-dropdown-menu>
                            <van-dropdown-item
                                v-model="currentFilter"
                                :options="filterOptions"
                                @change="onFilterChange"
                            />
                        </van-dropdown-menu>
                    </div>
                </div>
            </div>

            <!-- 列表内容 -->
            <div class="list-section">
                <!-- 加载状态 -->
                <van-loading v-if="loading" class="loading-center" size="24px" vertical>
                    加载中...
                </van-loading>

                <!-- 空状态 -->
                <van-empty v-else-if="filteredList.length === 0" description="暂无数据" />

                <!-- 出场管理列表 -->
                <div v-else class="exit-list">
                    <div 
                        v-for="item in filteredList" 
                        :key="item.id"
                        class="exit-item"
                    >
                        <div class="item-content">
                            <!-- 基本信息 -->
                            <div class="basic-info">
                                <div class="info-row">
                                    <span class="label">合同编号</span>
                                    <span class="value">{{ item.contractNo }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="label">承租方</span>
                                    <span class="value">{{ item.tenant }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="label">退租日期</span>
                                    <span class="value">{{ item.exitDate }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="label">退租房源数</span>
                                    <span class="value">{{ item.roomCount }}</span>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="action-section">
                                <van-button 
                                    type="primary" 
                                    size="small"
                                    @click="handleExit(item)"
                                    :disabled="item.status === '已办理'"
                                >
                                    {{ item.status === '已办理' ? '已完成' : '办理出场' }}
                                </van-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 出场提示弹窗 -->
        <van-dialog
            v-model:show="showExitDialog"
            :show-cancel-button="false"
            :show-confirm-button="false"
            class-name="exit-dialog"
        >
            <div class="exit-dialog-content">
                <!-- 出场提示标题 -->
                <div class="dialog-title">出场提示：</div>
                
                <!-- 提示内容 -->
                <div class="dialog-tips">
                    <div class="tip-item">1、房源可分批次做出场物业交割，但必须所有退租房源统一结算</div>
                    <div class="tip-item">2、物业交割单必须商服和物业全都确认，结算单才能发给客户签字</div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="dialog-actions">
                    <van-button 
                        type="primary" 
                        class="action-button"
                        @click="handlePropertyFirst"
                    >
                        先物业交割，后续结算
                    </van-button>
                    
                    <van-button 
                        type="primary" 
                        class="action-button"
                        @click="handlePropertyAndSettlement"
                    >
                        物业交割并结算
                    </van-button>
                    
                    <van-button 
                        type="default" 
                        class="action-button cancel-button"
                        @click="handleCancel"
                    >
                        取消
                    </van-button>
                </div>
            </div>
        </van-dialog>
    </div>
</template>

<style scoped>
.exit-management {
    min-height: 100vh;
    background-color: #f7f8fa;
}

.page-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 96px);
}

.search-section {
    padding: 16px;
    background-color: #fff;
}

.filter-section {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

.tab-filter-wrapper {
    display: flex;
    align-items: center;
    padding: 0 16px;
}

.tab-section {
    flex: 1;
}

.tab-section :deep(.van-tabs__nav) {
    background-color: transparent;
    border-bottom: none;
}

.tab-section :deep(.van-tabs__line) {
    background-color: #3583FF;
}

.tab-section :deep(.van-tab) {
    font-size: 30px;
    color: #666;
    padding: 0 16px;
}

.tab-section :deep(.van-tab--active) {
    color: #3583FF;
    font-weight: 600;
}

.filter-dropdown {
    width: 140px;
}

.filter-dropdown :deep(.van-dropdown-menu__bar) {
    background-color: transparent;
    box-shadow: none;
    height: auto;
}

.filter-dropdown :deep(.van-dropdown-menu__item) {
    justify-content: flex-end;
    padding: 0;
}

.filter-dropdown :deep(.van-dropdown-menu__title) {
    font-size: 30px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-dropdown :deep(.van-dropdown-menu__title::after) {
    border-color: #999 transparent transparent;
}

.list-section {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.exit-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.exit-item {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #f5f5f5;
}

.item-content {
    padding: 24px 20px;
}

.basic-info {
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 30px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.label {
    color: #666;
    width: 160px;
    flex-shrink: 0;
    font-size: 30px;
}

.value {
    color: #333;
    flex: 1;
    word-break: break-all;
    font-size: 30px;
    font-weight: 400;
    text-align: right;
}

.action-section {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #f5f5f5;
}

.action-section :deep(.van-button) {
    min-width: 10px;
    height: 58px;
    font-size: 28px;
    /* border-radius: 36px; */
    font-weight: 500;
}

/* 自定义 van-nav-bar 样式 */
:deep(.van-nav-bar .van-icon) {
    color: #333333;
}

/* 自定义搜索框样式 */
:deep(.van-search__content) {
    background-color: #f5f5f5;
    border-radius: 12px;
    border: none;
}

:deep(.van-search__field) {
    background-color: transparent;
    font-size: 30px;
    color: #333;
}

:deep(.van-search .van-field__left-icon) {
    color: #999;
}

:deep(.van-search__action) {
    font-size: 30px;
    color: #666;
}

/* 自定义下拉菜单样式 */
:deep(.van-dropdown-item__content) {
    max-height: 300px;
}

/* 出场提示弹窗样式 */
:deep(.exit-dialog) {
    border-radius: 16px;
    overflow: hidden;
}

:deep(.exit-dialog .van-dialog__content) {
    padding: 0;
}

.exit-dialog-content {
    padding: 32px 24px 24px;
}

.dialog-title {
    font-size: 32px;
    font-weight: 600;
    color: #FF4141;
    margin-bottom: 24px;
    text-align: left;
}

.dialog-tips {
    margin-bottom: 32px;
}

.tip-item {
    font-size: 28px;
    color: #333;
    line-height: 40px;
    margin-bottom: 16px;
    text-align: left;
}

.tip-item:last-child {
    margin-bottom: 0;
}

.dialog-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.action-button {
    width: 100%;
    height: 88px;
    font-size: 32px;
    border-radius: 8px;
}

.action-button:deep(.van-button__text) {
    font-weight: 500;
}

.cancel-button {
    background-color: #f7f8fa;
    color: #666;
    border: 1px solid #eee;
}

.cancel-button:deep(.van-button__text) {
    color: #666;
}
</style> 