<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showConfirmDialog, showDialog, showToast, showLoadingToast, closeToast } from 'vant'
import { getBookingDetail, payBooking, printBooking, getPricingAgreementDoc } from '../api/booking'
import type { BookingDetailVo, BookingPaymentDto } from '../api/booking'
import { formatMoney, formatDate } from '../utils/format'
import { renderAsync } from 'docx-preview'

// 路由
const router = useRouter()
const route = useRoute()

// 页面状态
const loading = ref(false)
const bookingDetail = ref<BookingDetailVo | null>(null)
const bookingId = ref<string>('')


// 支付金额（从接口获取）
const paymentAmount = computed(() => {
	if (!bookingDetail.value || !bookingDetail.value.booking) return '0.00'
	return formatMoney(bookingDetail.value.booking.unpaidAmount)
})

// 预定信息（从接口获取）
const reservationInfo = computed(() => {
	if (!bookingDetail.value || !bookingDetail.value.booking) {
		return {
			unitName: '',
			person: '',
			date: ''
		}
	}

	const booking = bookingDetail.value.booking
	return {
		unitName: booking.roomName || '',
		person: booking.customerName || '',
		date: formatDate(booking.receivableDate) || ''
	}
})

// 是否勾选同意协议
const isAgreed = ref(false)

// 判断是否是扫码进入（通过URL参数id判断）
const isScannedEntry = computed(() => {
	return !!route.query.id
})

// 获取定单ID
const getBookingId = () => {
	const id = route.query.id as string
	if (!id) {
		showToast('缺少定单ID参数')
		if (!isScannedEntry.value) {
			router.back()
		}
		return ''
	}
	return id
}

// 租赁定价协议内容
const docUrl = ref('')
const showAgreement = ref(false)
const loadingDoc = ref(false)
const fileName = ref('')
const mainDocContainer = ref<HTMLElement | null>(null)
const dialogDocContainer = ref<HTMLElement | null>(null)

// 监听文档容器的挂载
const docContainerMounted = ref(false)

onMounted(() => {
	// 设置页面背景色
	document.body.style.background = 'linear-gradient(to bottom, #3583FF 0%, #3583FF 10%, #F1F1F1 40%)';
	document.body.style.backgroundAttachment = 'fixed';

	// 获取定单ID并加载数据
	const id = getBookingId()
	if (id) {
		bookingId.value = id
		fetchBookingDetail(id)
	}

	// 标记文档容器已挂载
	docContainerMounted.value = true
})

// 获取定单详情
const fetchBookingDetail = async (id: string) => {
	try {
		loading.value = true
		const response = await getBookingDetail(id)
		console.log(response)
		bookingDetail.value = response.data
		
		// 等待文档容器挂载后再加载文档
		if (docContainerMounted.value) {
			await loadAgreementDoc()
		}

	} catch (error) {
		console.error('获取定单详情失败:', error)
		showToast('获取定单信息失败')
		setTimeout(() => router.back(), 2000)
	} finally {
		loading.value = false
	}
}

const loadAgreementDoc = async () => {
	try {
		loadingDoc.value = true
		const { data } = await getPricingAgreementDoc({ id: route.query.id as string, type: 10 })
		console.log('文档数据:', data)
		docUrl.value = data.fileUrl
		fileName.value = data.fileName
		
		// 获取文档内容并渲染
		
		let response = await fetch(data.fileUrl.replace('http://vyamuat.vanyang.com.cn', ''))
		// if (data.fileUrl.includes('http://vyamuat.vanyang.com.cn')) {
		// 	response = await fetch(data.fileUrl.replace('http://vyamuat.vanyang.com.cn', ''))
		// }else{
		// 	response = await fetch(data.fileUrl.replace('http://172.30.1.254:8570', ''))
		// }
		console.log('response:', response)
		const blob = await response.blob()
		
		console.log('mainDocContainer:', mainDocContainer.value)
		if (mainDocContainer.value) {
			// 清空容器内容
			mainDocContainer.value.innerHTML = ''
			
			// 添加加载提示
			const loadingDiv = document.createElement('div')
			loadingDiv.className = 'loading-container'
			loadingDiv.innerHTML = '<div>文档加载中...</div>'
			mainDocContainer.value.appendChild(loadingDiv)
			
			try {
				await renderAsync(blob, mainDocContainer.value, mainDocContainer.value, {
					className: 'docx-viewer',
					inWrapper: true,
					ignoreHeight: false,
					ignoreWidth: false,
					ignoreFonts: false,
					breakPages: true,
					useBase64URL: false
				})
			} finally {
				// 移除加载提示
				if (mainDocContainer.value.contains(loadingDiv)) {
					mainDocContainer.value.removeChild(loadingDiv)
				}
			}
		} else {
			console.error('文档容器不存在')
			// showToast('文档容器初始化失败')
		}
	} catch (error) {
		console.error('获取租赁定价协议失败:', error)
		// showToast('获取文档失败，请稍后重试')
	} finally {
		loadingDoc.value = false
	}
}

// 页面加载时获取数据
onMounted(async () => {
	// 设置页面背景色
	document.body.style.background = 'linear-gradient(to bottom, #3583FF 0%, #3583FF 10%, #F1F1F1 40%)';
	document.body.style.backgroundAttachment = 'fixed';

	// 获取定单ID并加载数据
	const id = getBookingId()
	if (id) {
		bookingId.value = id
		await fetchBookingDetail(id)
	}
})

onUnmounted(() => {
	// 恢复原来的背景色
	document.body.style.background = '';
	document.body.style.backgroundAttachment = '';
})

// 返回上一页
const goBack = () => {
	router.back()
}

// 打印定单
const handlePrint = async () => {
	if (!bookingId.value) return

	try {
		showLoadingToast({ message: '正在生成套打文件...', forbidClick: true })
		await printBooking(bookingId.value)
		closeToast()
		showToast('套打文件生成成功')
	} catch (error) {
		closeToast()
		console.error('套打失败:', error)
		showToast('套打文件生成失败')
	}
}

// 去支付方法
const handlePayment = async () => {
	if (!isAgreed.value) {
		showDialog({
			title: '提示',
			message: '请先同意租赁定金协议',
		})
		return
	}

	if (!bookingDetail.value || !bookingDetail.value.booking) {
		showToast('定单信息加载中，请稍后重试')
		return
	}

	const amount = bookingDetail.value.booking.unpaidAmount

	showConfirmDialog({
		title: '确认支付',
		message: `确认支付￥${formatMoney(amount)}?`,
	})
		.then(async () => {
			try {
				showLoadingToast({ message: '正在处理支付...', forbidClick: true })

				const paymentData: BookingPaymentDto = {
					bookingId: bookingId.value,
					amount: amount
					// amount: 0.01
				}

				const response = await payBooking(paymentData)
				closeToast()

				if (response.code === 200 && response.paymentUrl) {
					// 跳转到支付页面
					window.location.href = response.paymentUrl
				} else if (response.code === 200) {
					showToast('支付成功')
					// 如果是扫码进入，关闭浏览器；否则返回上一页
					setTimeout(() => {
						if (isScannedEntry.value) {
							window.close()
						} else {
							router.back()
						}
					}, 2000)
				} else {
					showToast(response.data.msg || '支付失败')
				}
			} catch (error) {
				closeToast()
				console.error('支付失败:', error)
				showToast('支付处理失败，请重试')
			}
		})
		.catch(() => {
			// 取消支付的处理逻辑
			console.log('取消支付')
		})
}

// 预览文档
const previewDoc = () => {
	if (docUrl.value) {
		// 使用新窗口打开预览页面
		const previewUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(docUrl.value)}`
		window.open(previewUrl, '_blank')
	}
}

// 格式化状态显示文本
const getStatusText = (status: number) => {
	const statusMap = {
		0: '草稿',
		1: '待收费',
		2: '已生效',
		3: '已转签',
		4: '已作废'
	}
	return statusMap[status as keyof typeof statusMap] || '未知状态'
}
</script>

<template>
	<div class="order-payment">
		<!-- 顶部导航栏 -->
		<van-nav-bar title="定单缴费" :left-arrow="!isScannedEntry" fixed placeholder @click-left="goBack">
			<template #right v-if="bookingDetail">
				<!-- <van-button size="small" type="primary" plain @click="handlePrint">
					套打
				</van-button> -->
			</template>
		</van-nav-bar>

		<!-- 加载状态 -->
		<van-loading v-if="loading" class="loading-container" vertical>
			<template #icon>
				<van-icon name="spinner" class="loading-icon" />
			</template>
			正在加载定单信息...
		</van-loading>

		<!-- 主要内容 -->
		<div v-else-if="bookingDetail" class="payment-content">
			<!-- 订单信息卡片 -->
			<div class="order-card">
				<!-- 卡片头部背景图 -->
				<div class="card-header"></div>

				<!-- 订单详情 -->
				<div class="card-body">
					<div class="card-title-area">
						<h2 class="unit-title">预定租赁单元</h2>
						<div class="unit-name">
							<strong>{{ reservationInfo.unitName }}</strong>
						</div>
						<div v-if="bookingDetail" class="status-badge"
							:class="`status-${bookingDetail.booking.status}`">
							{{ getStatusText(bookingDetail.booking.status) }}
						</div>
					</div>

					<!-- 预定人和预定日期 -->
					<div class="reservation-info reservation-info-between">
						<div class="info-item">
							<span class="info-label">预定人：</span>
							<span class="info-value">{{ reservationInfo.person }}</span>
						</div>
						<div class="info-item">
							<span class="info-label">预定日期：</span>
							<span class="info-value">{{ reservationInfo.date }}</span>
						</div>
					</div>

					<div class="divider"></div>

					<div class="cost-details">
						<!-- <div class="info-row">
							<span>定单总额：</span>
							<span class="amount">￥{{ formatMoney(bookingDetail.booking.bookingAmount) }}</span>
						</div>
						<div class="info-row">
							<span>已收金额：</span>
							<span class="amount">￥{{ formatMoney(bookingDetail.booking.receivedAmount) }}</span>
						</div> -->
						<div class="info-row">
							<span>收款金额：</span>
							<span class="amount highlight">￥{{ paymentAmount }}</span>
						</div>
					</div>

					<!-- 定单信息 -->
					<!-- <div v-if="bookingDetail && bookingDetail.booking && bookingDetail.cost" class="booking-info">
						<div class="info-item">
							<span class="info-label">定单号：</span>
							<span class="info-value">{{ bookingDetail.booking.bookingNo }}</span>
						</div>
						<div class="info-item">
							<span class="info-label">费用类型：</span>
							<span class="info-value">{{ bookingDetail.cost.subjectName }}</span>
						</div>
					</div>

					<div class="divider"></div> -->

					<!-- 费用明细 -->
					<!-- <div v-if="bookingDetail && bookingDetail.booking && bookingDetail.cost" class="cost-details">
						<div class="info-row">
							<span>定单总额：</span>
							<span class="amount">￥{{ formatMoney(bookingDetail.booking.bookingAmount) }}</span>
						</div>
						<div class="info-row">
							<span>已收金额：</span>
							<span class="amount">￥{{ formatMoney(bookingDetail.booking.receivedAmount) }}</span>
						</div>
						<div class="info-row">
							<span>待收金额：</span>
							<span class="amount highlight">￥{{ paymentAmount }}</span>
						</div>
					</div> -->
				</div>
			</div>

			<!-- 租赁定价协议内容 -->
			<div class="agreement-content">
				<!-- <h3 class="agreement-title">租赁定价协议</h3> -->
				<div class="agreement-text">
					<div ref="mainDocContainer" class="doc-container"></div>

					<!-- <div v-if="loadingDoc" class="loading-container">
						<van-loading type="spinner" />
						<span>文档加载中...</span>
					</div>
					<div v-else ref="mainDocContainer" class="doc-container"></div> -->
				</div>
			</div>

			<!-- 协议勾选框 -->
			<div class="agreement-check">
				<van-checkbox v-model="isAgreed">我已知晓并同意定金协议</van-checkbox>
			</div>
		</div>

		<!-- 底部支付栏 -->
		<div v-if="bookingDetail" class="payment-footer">
			<div class="total-container">
				<span class="total-label">合计</span>
				<span class="total-amount">￥{{ paymentAmount }}</span>
			</div>
			<van-button type="primary" class="pay-button"
				:disabled="!isAgreed || bookingDetail.booking.unpaidAmount <= 0" @click="handlePayment">
				去支付
			</van-button>
		</div>

		<!-- 空状态 -->
		<van-empty v-else-if="!loading" description="未找到定单信息" />

		<!-- 租赁定价协议弹窗 -->
		<van-dialog
			v-model:show="showAgreement"
			title="租赁定价协议"
			:show-confirm-button="false"
			class="agreement-dialog"
		>
			<div v-if="loadingDoc" class="loading-container">
				<van-loading type="spinner" />
				<span>文档加载中...</span>
			</div>
			<div v-else ref="dialogDocContainer" class="doc-container"></div>
		</van-dialog>
	</div>
</template>

<style scoped>
:deep(.docx-viewer-wrapper) {
	width: 100% !important;
	height: 100% !important;
	overflow: hidden !important;
	padding: 0 !important;
	background: #fff !important;
}

:deep(.docx-viewer) {
	width: 100% !important;
	height: 100% !important;
	min-height: auto !important;
	overflow: auto !important;
	padding: 0 !important;
	margin-bottom: 0 !important;
}

.order-payment {
	height: 100vh;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

.loading-container {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	color: #fff;
	font-size: 28px;
}

.loading-icon {
	font-size: 60px;
	margin-bottom: 20px;
}

:deep(.van-nav-bar) {
	background-color: transparent;
	border-bottom: none;
}

:deep(.van-nav-bar__content) {
	border-bottom-width: 0;
}

:deep(.van-hairline--bottom::after) {
	border-bottom-width: 0;
}

:deep(.van-nav-bar .van-icon),
:deep(.van-nav-bar__title) {
	color: #fff;
}

:deep(.van-nav-bar__right .van-button) {
	color: #fff;
	border-color: rgba(255, 255, 255, 0.5);
}

.payment-content {
	flex: 1;
	padding: 0 30px 30px;
	margin-bottom: 160px;
	margin-top: 20px;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.order-card {
	flex: 0 0 auto;
	background: #fff;
	border-radius: 0 0 20px 20px;
	overflow: hidden;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
	margin-bottom: 40px;
	position: relative;
}

.card-header {
	height: 12px;
	background: linear-gradient(90deg, #3583FF 0%, #5CBFFF 100%);
	position: relative;
}

.card-body {
	padding: 40px 32px;
}

.card-title-area {
	margin-bottom: 20px;
	position: relative;
}

.unit-title {
	font-size: 26px;
	font-weight: 500;
	color: #919199;
	margin: 0;
	line-height: 1.4;
}

.unit-name {
	font-size: 28px;
	color: #000;
	line-height: 1.4;
	margin-top: 6px;
	font-weight: bold;
}

.unit-name strong {
	font-weight: 600;
	color: #6e6e77;
}

.status-badge {
	position: absolute;
	top: 0;
	right: 0;
	padding: 8px 16px;
	border-radius: 16px;
	font-size: 20px;
	color: #fff;
}

.status-badge.status-0 {
	background-color: #999;
}

.status-badge.status-1 {
	background-color: #ff9500;
}

.status-badge.status-2 {
	background-color: #07c160;
}

.status-badge.status-3 {
	background-color: #1989fa;
}

.status-badge.status-4 {
	background-color: #ee0a24;
}

.reservation-info,
.booking-info {
	display: flex;
	flex-direction: column;
	gap: 12px;
	margin: 24px 0;
}

.reservation-info-between {
	display: flex;
	align-items: center;
	flex-direction: row;
	justify-content: space-between;
}

.info-item {
	display: flex;
	font-size: 24px;
	color: #919199;
	line-height: 1.4;
}

.info-label {
	min-width: 120px;
}

.info-value {
	color: #6e6e77;
	font-weight: 500;
	flex: 1;
}

.divider {
	height: 1px;
	background-color: #EEEEEE;
	margin: 32px 0;
}

.cost-details .info-row {
	display: flex;
	justify-content: space-between;
	font-size: 24px;
	color: #919199;
	margin-bottom: 16px;
	line-height: 1.4;
}

.cost-details .info-row:last-child {
	margin-bottom: 0;
	font-size: 28px;
	font-weight: 600;
}

.amount {
	font-weight: 500;
	color: #6e6e77;
}

.amount.discount {
	color: #07c160;
}

.amount.highlight {
	color: #FF6900;
	font-size: 32px;
}

.agreement-content {
	flex: 1;
	background: #fff;
	border-radius: 20px;
	padding: 20px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
	margin-bottom: 20px;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	min-height: 500px;
}

.agreement-title {
	flex: 0 0 auto;
	font-size: 28px;
	font-weight: 500;
	color: #333;
	margin: 0 0 20px 0;
	text-align: center;
}

.agreement-text {
	flex: 1;
	overflow: hidden;
	position: relative;
	display: flex;
	flex-direction: column;
}

.doc-container {
	flex: 1;
	overflow: auto;
	-webkit-overflow-scrolling: touch;
	position: relative;
	display: flex;
	justify-content: center;
}

/* :deep(.docx-viewer) {
	padding: 10px;
	background: #fff;
	transform-origin: top center;
	transform: scale(1);	
	max-width: 1000px;
	margin: 0 auto;
} */

:deep(.docx-wrapper) {
	padding: 0;
	margin: 0;
}

/* 调整文档中的字体大小 */
:deep(.docx-viewer p) {
	margin: 0;
	padding: 0;
	line-height: 1.5;
}

.agreement-check {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	padding: 32px;
	border-radius: 0 0 20px 20px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
	position: relative;
}

:deep(.van-checkbox__label) {
	font-size: 26px;
}

.payment-footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32px;
	box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.04);
	z-index: 10;
	height: 160px;
}

.total-container {
	display: flex;
	flex-direction: column;
}

.total-label {
	font-size: 26px;
	font-weight: 500;
	color: #242433;
	line-height: 1.4;
}

.total-amount {
	font-size: 34px;
	font-weight: 500;
	color: #FF6900;
	line-height: 1.4;
	margin-top: 4px;
}

.pay-button {
	width: 240px;
	background: #3583FF;
	border-radius: 45px;
	font-size: 34px;
	font-weight: 500;
	height: 90px;
	padding: 0 40px;
	border: none;
	color: #fff;
}

:deep(.van-button--disabled) {
	opacity: 0.6;
	background: #3583FF;
}

:deep(.van-dialog) {
	border-radius: 20px;
	overflow: hidden;
}

:deep(.van-dialog__header) {
	font-weight: 500;
	padding-top: 30px;
}

:deep(.van-dialog__message) {
	padding: 30px;
	font-size: 26px;
	line-height: 1.6;
	white-space: pre-line;
	text-align: left;
}

:deep(.van-dialog__confirm) {
	color: #3583FF !important;
}

.agreement-dialog {
	:deep(.van-dialog) {
		width: 90%;
		height: 80vh;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 200px;
		gap: 16px;
	}
}
</style>