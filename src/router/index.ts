import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'), // 懒加载首页组件
    meta: {
      title: '万洋资管平台'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'), // 懒加载登录页面组件
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/crm-dashboard',
    name: 'CrmDashboard',
    component: () => import('../views/CrmDashboard.vue'), // 懒加载CRM仪表板组件
    meta: {
      title: '维享信CRM平台'
    }
  },
  {
    path: '/order-payment',
    name: 'OrderPayment',
    component: () => import('../views/OrderPayment.vue'), // 懒加载定单缴费组件
    meta: {
      title: '定单缴费'
    }
  },
  {
    path: '/bill-payment',
    name: 'BillPayment',
    component: () => import('../views/BillPayment.vue'), // 懒加载账单缴费组件
    meta: {
      title: '账单缴费'
    }
  },
  {
    path: '/entrance-notice',
    name: 'EntranceNotice',
    component: () => import('../views/EntranceNotice.vue'), // 懒加载进场通知单组件
    meta: {
      title: '进场通知单'
    }
  },
  {
    path: '/entry-management',
    name: 'EntryManagement',
    component: () => import('../views/EntryManagement.vue'), // 懒加载进场管理列表组件
    meta: {
      title: '进场管理'
    }
  },
  {
    path: '/entry-process',
    name: 'EntryProcess',
    component: () => import('../views/EntryProcess.vue'), // 懒加载进场办理组件
    meta: {
      title: '进场办理'
    }
  },
  {
    path: '/entry-detail',
    name: 'EntryDetail',
    component: () => import('../views/EntryDetail.vue'), // 懒加载进场详情组件
    meta: {
      title: '进场详情'
    }
  },
  {
    path: '/property-handover',
    name: 'PropertyHandover',
    component: () => import('../views/PropertyHandover.vue'), // 懒加载物业交割单确认组件
    meta: {
      title: '物业交割单确认'
    }
  },
  {
    path: '/property-handover-detail',
    name: 'PropertyHandoverDetail',
    component: () => import('../views/PropertyHandoverDetail.vue'), // 懒加载物业交割单详情页面组件
    meta: {
      title: '物业交割单详情'
    }
  },
  {
    path: '/exit-form-signature',
    name: 'ExitFormSignature',
    component: () => import('../views/ExitFormSignature.vue'), // 懒加载退场单签字确认组件
    meta: {
      title: '出场单签字确认'
    }
  },
  {
    path: '/exit-management',
    name: 'ExitManagement',
    component: () => import('../views/ExitManagement.vue'), // 懒加载出场管理组件
    meta: {
      title: '出场管理'
    }
  },
  {
    path: '/booking-list',
    name: 'BookingList',
    component: () => import('../views/BookingList.vue'), // 懒加载定单列表组件
    meta: {
      title: '定单'
    }
  },
  {
    path: '/booking-detail/:id?',
    name: 'BookingDetail',
    component: () => import('../views/BookingDetail.vue'), // 懒加载定单详情组件
    meta: {
      title: '定单详情'
    }
  },
  {
    path: '/booking-create',
    name: 'BookingCreate',
    component: () => import('@/views/BookingCreate.vue')
  },
  {
    path: '/refund-booking/:id?',
    name: 'RefundBooking',
    component: () => import('../views/RefundBooking.vue'),
    meta: {
      title: '退定单'
    }
  },
  {
    path: '/test-signature',
    name: 'TestSignature',
    component: () => import('../views/TestSignature.vue'), // 懒加载签字组件测试页面
    meta: {
      title: '签字组件测试'
    }
  },
  {
    path: '/test-water-electric',
    name: 'TestWaterElectric',
    component: () => import('../views/TestWaterElectric.vue'), // 懒加载水电费功能测试页面
    meta: {
      title: '水电费功能测试'
    }
  },
  {
    path: '/test-property-sign',
    name: 'TestPropertySign',
    component: () => import('../views/TestPropertySign.vue'), // 懒加载物业签字流程测试页面
    meta: {
      title: '物业签字流程测试'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 全局前置守卫，设置页面标题
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title as string || '万洋资管平台'
  next()
})

export default router 