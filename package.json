{"name": "wyzgpt-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev20": "vite", "build:dev": "vite build --mode dev", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode prod", "upload": "node upload.server.js", "uploadDev": "node upload.server.js dev", "uploadUat": "node upload.server.js uat", "uploadProd": "node upload.server.js prod", "uploadProd2": "node upload.server.js prod2", "buildAndupload": "npm run build && npm run upload", "buildAnduploadDev": "npm run build && npm run uploadDev", "buildAnduploadUat": "npm run build && npm run uploadUat", "buildAnduploadProd": "npm run build && npm run uploadProd && npm run uploadProd2", "buildAnduploadProd2": "npm run build && npm run uploadProd2", "preview": "vite preview"}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^1.9.0", "docx-preview": "^0.3.5", "less": "^4.3.0", "postcss-pxtorem": "^6.1.0", "qrcode-generator": "^2.0.2", "ssh2-sftp-client": "^12.0.0", "vant": "^4.9.19", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}